{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@webcontainer/api": "^1.5.1-internal.4", "axios": "^1.7.9", "highlight.js": "^11.11.0", "markdown-to-jsx": "^7.7.1", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^7.0.2", "remixicon": "^4.5.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@eslint/js": "^9.15.0", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.15.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.14", "globals": "^15.12.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "vite": "^6.0.1"}}